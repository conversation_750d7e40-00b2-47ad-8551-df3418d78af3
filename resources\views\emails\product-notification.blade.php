<!DOCTYPE html> 
<html lang="vi"> 
<head> 
<meta charset="UTF-8"> 
<meta name="viewport" content="width=device-width, initial-scale=1.0"> 
<title>Thông Báo Đơn Hàng Mới - {{ $productMain->name }}</title> 
<style> 
/* giữ nguyên style */
</style> 
</head> 
<body> 
<div class="container"> 
<!-- Header --> 
<div class="header"> 
<h1>🎉 Có Đơn Hàng Mới!</h1> 
<p>Sản Phẩm Bạn Quan Tâm Vừa Có Người Mua!</p> 
</div> 

<!-- Content --> 
<div class="content"> 
<div class="greeting"> 
<strong>Xin Chào,</strong> 
</div> 

<p>Chúng Tôi Vui Mừng Thông Báo Rằng Sản Phẩm <strong>{{ $productMain->name }}</strong> Mà Bạn Đã Đăng Ký Nhận Thông Báo Vừa Có Đơn Hàng Mới!</p> 

<!-- Product Showcase --> 
<div class="product-showcase"> 
<img src="{{ asset($productMain->image) }}" alt="{{ $productMain->name }}" class="product-image"> 

<div class="product-name">{{ $productMain->name }}</div> 

<div class="product-description"> 
{!! Str::limit(strip_tags($productMain->description), 200) !!}
</div> 

<div class="price"> 
@if($productMain->product->where('domain', request()->getHost())->where('status', 'active')->count() > 0)
    @php
        $minPrice = $productMain->product->where('domain', request()->getHost())->where('status', 'active')->min('price');
        $maxPrice = $productMain->product->where('domain', request()->getHost())->where('status', 'active')->max('price');
    @endphp
    @if($minPrice == $maxPrice)
        <span>{{ number_format($minPrice) }}₫</span>
    @else
        <span>{{ number_format($minPrice) }}₫ - {{ number_format($maxPrice) }}₫</span>
    @endif
@else
    <span>Liên Hệ</span>
@endif
</div> 

<div class="features"> 
<h3>✨ Thông Tin Sản Phẩm:</h3> 
<ul> 
<li>Sản Phẩm Chất Lượng Cao</li> 
<li>Giao Hàng Nhanh Chóng</li> 
<li>Hỗ Trợ Khách Hàng 24/7</li> 
<li>Đã Bán: {{ number_format($productMain->sold >= 100 ? $productMain->sold : $productMain->sold + 100) }} Lượt</li> 
<li>Đang Có Sẵn Hàng</li> 
</ul> 
</div> 

<a href="{{ route('client.product', ['slug' => $productMain->slug]) }}" class="cta-button">🛒 Xem Sản Phẩm</a> 
</div> 

<!-- Special Offer --> 
<div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0;"> 
<p style="color: #856404; margin: 5px 0;">• Sản Phẩm Đang Được Quan Tâm</p> 
<p style="color: #856404; margin: 5px 0;">• Số Lượng Có Hạn</p> 
</div> 

<!-- Contact Information --> 
<div class="contact-info"> 
<h3>📞 Liên Hệ Với Chúng Tôi:</h3> 
<p><strong>Website:</strong> {{ request()->getHost() }}</p> 
<p><strong>Email:</strong> {{ siteValue('smtp_user') }}</p> 
@if(siteValue('nameadmin'))
<p><strong>Hỗ Trợ:</strong> {{ siteValue('nameadmin') }}</p>
@endif
</div> 

<p>Cảm Ơn Bạn Đã Quan Tâm Đến Sản Phẩm Của Chúng Tôi. Chúng Tôi Cam Kết Mang Đến Những Sản Phẩm Chất Lượng Nhất Với Dịch Vụ Tốt Nhất.</p> 

<p style="margin-top: 30px;"> 
<strong>Trân Trọng,</strong><br> 
<strong>Đội Ngũ {{ siteValue('name_site') ?: request()->getHost() }}</strong> 
</p> 
</div> 

<!-- Footer --> 
<div class="footer"> 
<p>&copy; {{ date('Y') }} {{ siteValue('name_site') ?: request()->getHost() }}. Mọi Quyền Được Bảo Lưu.</p> 
<p>Bạn Nhận Được Email Này Vì Đã Đăng Ký Nhận Thông Báo Từ Chúng Tôi.</p> 
</div> 
</div> 
</body> 
</html>

