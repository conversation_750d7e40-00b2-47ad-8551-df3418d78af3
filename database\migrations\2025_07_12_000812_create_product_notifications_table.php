<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_notifications', function (Blueprint $table) {
            $table->id();
            $table->string('email')->comment('Email đăng ký nhận thông báo');
            $table->unsignedBigInteger('product_main_id')->comment('ID sản phẩm chính');
            $table->string('domain')->comment('Domain của website');
            $table->enum('status', ['active', 'inactive'])->default('active')->comment('Trạng thái đăng ký');
            $table->timestamps();

            // Index để tối ưu truy vấn
            $table->index(['product_main_id', 'domain', 'status']);
            $table->index(['email', 'domain']);

            // Foreign key constraint
            $table->foreign('product_main_id')->references('id')->on('product_mains')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_notifications');
    }
};
