<?php

use App\Http\Controllers\Admin\DataAdminController;
use App\Http\Controllers\Admin\HistoryController;
use App\Http\Controllers\Admin\NotificationController;
use App\Http\Controllers\Admin\PartnerWebsiteController;
use App\Http\Controllers\Admin\PaymentController;
use App\Http\Controllers\Admin\ServiceController;
use App\Http\Controllers\Admin\SmmController;
use App\Http\Controllers\Admin\TicketsController;
use App\Http\Controllers\Admin\ServicePlatformController;
use App\Http\Controllers\Admin\ServiceServerController;
use App\Http\Controllers\Admin\VoucherController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\AffiliatesController;
use App\Http\Controllers\Admin\ViewAdminController;

use App\Http\Controllers\Guard\ClientProductController;
use App\Http\Controllers\Admin\Product\CategoryController;
use App\Http\Controllers\Admin\Product\ProductChildController;
use App\Http\Controllers\Admin\Product\ProductController;
use App\Http\Controllers\Admin\ProductionController;
use App\Http\Controllers\Auth\AuthenticateController;
use App\Http\Controllers\Guard\AccountController;
use App\Http\Controllers\Guard\TicketController;
use App\Http\Controllers\Guard\AffiliateController;
use App\Http\Controllers\Guard\ViewGuardController;
use App\Http\Controllers\Guard\WebSiteController;
use App\Http\Controllers\Service\ViewServiceController; 
use App\Http\Controllers\Admin\DiscordController;
use App\Http\Controllers\Admin\AutomationController;

use Illuminate\Support\Facades\Route;

Route::prefix('site')->middleware(['installSite'])->group(function () {
    Route::get('install', [AuthenticateController::class, 'viewInstall'])->name('install');
    Route::post('install', [AuthenticateController::class, 'install'])->name('install.post');
});
Route::get('/ref/{id}', [AuthenticateController::class, 'RefPage'])->name('ref');
Route::get('auth/logout', [AuthenticateController::class, 'logout'])->name('logout');
Route::prefix('auth')->middleware(['guest', 'xss'])->group(function () {
    Route::get('login', [AuthenticateController::class, 'viewLogin'])->name('login');
    Route::get('register', [AuthenticateController::class, 'viewRegister'])->name('register');

    Route::post('login', [AuthenticateController::class, 'login'])->name('login.post');
    Route::post('register', [AuthenticateController::class, 'register'])->name('register.post');
});
Route::get('/', function () {
    return view('landing');
    // return abort(404);
    // return redirect()->route('home');
})->name('landing');


Route::prefix('/')->group(function () {
    Route::prefix('/user')->group(function () {
        Route::get('/get-data', [ViewGuardController::class, 'GetData'])->name('client.get-data');
    });

    Route::prefix('product')->group(function(){
        Route::get('/categories', [ClientProductController::class, 'CategoriesView'])->name('client.product.categories');
        Route::get('/view-category/{slug}', [ClientProductController::class, 'CategoryView'])->name('client.product.category');
        Route::get('/view/{slug}', [ClientProductController::class, 'ProductView'])->name('client.product');

        

        Route::get('/bought', [ClientProductController::class, 'BoughtView'])->name('client.product.bought')->middleware(['auth']);
        Route::post('/download/{id}', [ClientProductController::class, 'DownloadProduct'])->name('client.product.download')->middleware(['auth']);
        Route::delete('/delete-data/{id}', [ClientProductController::class, 'DeleteProductData'])->name('client.product.delete-data')->middleware(['auth']);
    });
    Route::get('home', [ViewGuardController::class, 'viewHome'])->name('home');
    Route::get('rule', [ViewGuardController::class, 'viewRule'])->name('rule');
    Route::get('apiv2', [ViewGuardController::class, 'viewApi'])->name('api');
    Route::get('new', [ViewGuardController::class, 'viewNew'])->name('new');
    Route::get('support', [ViewGuardController::class, 'viewSupport'])->name('support');
    Route::get('support/contact', [ViewGuardController::class, 'viewSupportContact'])->name('support.contact');
    Route::get('affiliate', [ViewGuardController::class, 'viewAffiliate'])->name('affiliate')->middleware(['auth']);
    if (site('status_smm') == 'on') {
    Route::get('order', [ViewGuardController::class, 'viewOrder'])->name('order');
    }
    if (site('status_massorder') == 'on') {
        Route::get('massorder', [ViewGuardController::class, 'viewMass'])->name('mass')->middleware(['auth']);
    }
    Route::post('/service/checking', [DataAdminController::class, 'serviceChecking'])->name('service.checking.post');
    Route::post('/service/server/checking', [DataAdminController::class, 'serverChecking'])->name('service.server.checking.post');
    Route::post('/server/checking', [DataAdminController::class, 'serverserviceChecking'])->name('server.checking.post');
    Route::prefix('account')->middleware(['auth'])->group(function () {
        Route::get('profile', [ViewGuardController::class, 'viewProfile'])->name('account.profile');
        Route::get('recharge', [ViewGuardController::class, 'viewRecharge'])->name('account.recharge');
        Route::get('card', [ViewGuardController::class, 'viewCard'])->name('account.card');
        Route::post('card', [ViewGuardController::class, 'Card'])->name('account.card.post');
        Route::get('recharge/payment/{id}', [ViewGuardController::class, 'viewCreateRecharge'])->name('account.recharge.payment');
        Route::post('recharge', [ViewGuardController::class, 'createRecharge'])->name('recharge.post');
        Route::get('transactions', [ViewGuardController::class, 'viewTransactions'])->name('account.transactions');
        Route::get('progress', [ViewGuardController::class, 'viewProgress'])->name('account.progress');
        //checking
        Route::get('services', [ViewGuardController::class, 'viewServices'])->name('account.services');
        Route::post('change-password', [AccountController::class, 'changePassword'])->name('account.change-password');
        Route::post('two-factor-auth', [AccountController::class, 'twoFactorAuth'])->name('account.two-factor-auth');
        Route::post('two-factor-auth-disable', [AccountController::class, 'twoFactorAuthDisable'])->name('account.two-factor-auth-disable');
        Route::get('reload-user-token', [AccountController::class, 'reloadUserToken'])->name('account.reload-user-token');
        Route::post('update/status-discord', [AccountController::class, 'updateStatusDiscord'])->name('account.update.status-discord');

        Route::get('/action-mode', [AccountController::class, 'actionMode'])->name('account.action-mode');
    });

    Route::get('ticket', [TicketController::class, 'viewTicket'])->name('ticket')->middleware(['auth']);
    Route::get('ticket/{id}', [TicketController::class, 'viewEditTicket'])->name('ticket.edit')->middleware(['auth']);
    Route::post('ticket', [TicketController::class, 'createTicket'])->name('ticket.post')->middleware(['auth']);
    Route::get('create/website', [WebSiteController::class, 'viewCreateWebsite'])->name('create.website')->middleware(['auth']);
    Route::post('create/website', [WebSiteController::class, 'createWebsite'])->name('create.website.post')->middleware(['auth']);
    Route::get('withdraw', [AffiliateController::class, 'viewWithdraw'])->name('withdraw')->middleware(['auth']);
    Route::post('create/withdraw', [AffiliateController::class, 'createWithdraw'])->name('withdraw.create')->middleware(['auth']);
    Route::get('service/{platform}/{service}', [ViewServiceController::class, 'viewService'])->name('service');
     
});

// admin
Route::prefix('admin')->middleware(['auth', 'isAdmin'])->group(function () {
    
Route::get('/get-data', [DataAdminController::class, 'getData'])->name('admin.get-data');
Route::delete('/delete-data', [DataAdminController::class, 'deleteData'])->name('admin.delete-data');
    Route::prefix('/products')->group(function () {
    Route::get('/categories', [CategoryController::class, 'categories'])->name('admin.products.categories');
    Route::get('/categories/create', [CategoryController::class, 'categoryCreate'])->name('admin.products.categories.create');
    Route::post('/categories/store', [CategoryController::class, 'categoryStore'])->name('admin.products.categories.store');
    Route::get('/categories/edit/{id}', [CategoryController::class, 'categoryEdit'])->name('admin.products.categories.edit');
    Route::post('/categories/update/{id}', [CategoryController::class, 'categoryUpdate'])->name('admin.products.categories.update');

    Route::get('/', [ProductController::class, 'products'])->name('admin.products');
    Route::get('/create', [ProductController::class, 'create'])->name('admin.products.create');
    Route::post('/store', [ProductController::class, 'store'])->name('admin.products.store');
    Route::get('/edit/{id}', [ProductController::class, 'edit'])->name('admin.products.edit');
    Route::post('/update/{id}', [ProductController::class, 'update'])->name('admin.products.update');

    # sản phẩm con
    Route::get('/child', [ProductChildController::class, 'child'])->name('admin.products.child');
    Route::get('/child/create', [ProductChildController::class, 'create'])->name('admin.products.child.create');
    Route::post('/child/store', [ProductChildController::class, 'store'])->name('admin.products.child.store');
    Route::get('/child/edit/{id}', [ProductChildController::class, 'edit'])->name('admin.products.child.edit');
    Route::post('/child/update/{id}', [ProductChildController::class, 'update'])->name('admin.products.child.update');

    // thêm kho cho sản phẩm con
    Route::post('/child/inventory/store', [ProductChildController::class, 'storeInventory'])->name('admin.products.child.inventory.store');
});
    Route::get('dashboard', [ViewAdminController::class, 'viewDashboard'])->name('admin.dashboard');
    Route::get('website/config', [ViewAdminController::class, 'viewWebsiteConfig'])->name('admin.website.config');
    Route::get('rules/manage', [ViewAdminController::class, 'viewRulesManage'])->name('admin.rules.manage');
    Route::get('cron/', [ViewAdminController::class, 'viewCron'])->name('admin.cron');


    Route::get('notify/', [NotificationController::class, 'viewNotify'])->name('admin.notify');
    Route::post('notify/system/create', [NotificationController::class, 'createSystemNotify'])->name('admin.notify.system.create');
    Route::get('notify/system/delete/{id}', [NotificationController::class, 'deleteSystemNotify'])->name('admin.notify.system.delete');
    Route::post('notify/service/create', [NotificationController::class, 'createServiceNotify'])->name('admin.notify.service.create');
    Route::get('notify/service/delete/{id}', [NotificationController::class, 'deleteServiceNotify'])->name('admin.notify.service.delete');

    Route::get('payment/config', [PaymentController::class, 'viewPaymentConfig'])->name('admin.payment.config');
    Route::post('payment/config/update', [PaymentController::class, 'updatePaymentConfig'])->name('admin.payment.config.update');
    Route::post('payment/update/{bank_name}', [PaymentController::class, 'updatePayment'])->name('admin.payment.update');
    Route::get('website/partner', [PartnerWebsiteController::class, 'viewPartnerWebsite'])->name('admin.website.partner');
    Route::get('website/partner/edit/{id}', [PartnerWebsiteController::class, 'viewEditPartnerWebsite'])->name('admin.website.partner.edit');
    Route::post('website/partner/update/{id}', [PartnerWebsiteController::class, 'updatePartnerWebsite'])->name('admin.website.partner.update');
    Route::get('website/partner/active/{id}', [PartnerWebsiteController::class, 'activePartnerWebsite'])->name('admin.website.partner.active');
    Route::get('/website/partner/{id}/reset', [PartnerWebsiteController::class, 'resetPartnerWebsite'])->name('admin.website.partner.reset');
    Route::get('website/partner/delete/{id}', [PartnerWebsiteController::class, 'deletePartnerWebsite'])->name('admin.website.partner.delete');

    // website con
    if (request()->getHost() === env('APP_MAIN_SITE')) {
        Route::post('/smm/checking', [ServiceServerController::class, 'smmChecking'])->name('admin.smm.checking.post');
        Route::post('/smm/seice/checking', [ServiceServerController::class, 'smmserviceeeChecking'])->name('admin.smm.serv.checking.post');
        Route::post('/smm/service/checking', [ServiceServerController::class, 'smmserviceChecking'])->name('admin.smm.service.checking.post');

        // dịch vụ & và nền tảng
        Route::get('service/platform', [ServicePlatformController::class, 'viewServicePlatform'])->name('admin.service.platform');
        Route::post('service/platform/create', [ServicePlatformController::class, 'createServicePlatform'])->name('admin.service.platform.create');
        Route::get('service/platform/edit/{id}', [ServicePlatformController::class, 'viewEditServicePlatform'])->name('admin.service.platform.edit');
        Route::post('service/platform/update/{id}', [ServicePlatformController::class, 'updateServicePlatform'])->name('admin.service.platform.update');
        Route::get('service/platform/delete/{id}', [ServicePlatformController::class, 'deleteServicePlatform'])->name('admin.service.platform.delete');
        // smm
        Route::get('service/smm', [SmmController::class, 'viewSmm'])->name('admin.service.smm');
        Route::post('service/smm/create', [SmmController::class, 'createSmm'])->name('admin.service.smm.create');
        Route::get('service/smm/edit/{id}', [SmmController::class, 'viewEditSmm'])->name('admin.service.smm.edit');
        Route::get('service/smm/balance/', [SmmController::class, 'balanceSmm'])->name('admin.service.smm.balance');
        Route::post('service/smm/update/{id}', [SmmController::class, 'updateSmm'])->name('admin.service.smm.update');
        Route::get('service/smm/delete/{id}', [SmmController::class, 'deleteSmm'])->name('admin.service.smm.delete');

        //ticket
        Route::get('service', [ServiceController::class, 'viewService'])->name('admin.service');
        Route::post('service/create', [ServiceController::class, 'createService'])->name('admin.service.create');
        Route::post('service/create/v2', [ServiceController::class, 'createServiceV2'])->name('admin.service.create.v2');
        Route::get('service/edit/{id}', [ServiceController::class, 'viewEditService'])->name('admin.service.edit');
        Route::post('service/update/{id}', [ServiceController::class, 'updateService'])->name('admin.service.update');
        Route::get('service/delete/{id}', [ServiceController::class, 'deleteService'])->name('admin.service.delete');
        // -- Máy chủ
        Route::post('/service/server/create', [ServiceServerController::class, 'createServer'])->name('admin.server.create');
        Route::post('/service/server/create/v2', [ServiceServerController::class, 'createServerV2'])->name('admin.server.create.v2');
        // Route GET để xử lý lỗi khi ai đó truy cập bằng GET method
        Route::get('/service/server/create/v2', function() {
            return redirect()->route('admin.server')->with('error', 'Phương thức GET không được hỗ trợ cho route này. Vui lòng sử dụng form để tạo máy chủ.');
        });
        Route::get('/service/server/delete/{id}', [ServiceServerController::class, 'deleteServer'])->name('admin.server.delete');
        Route::post('/service/server/delete/checked', [ServiceServerController::class, 'deleteServerChecked'])->name('admin.server.delete.checked');
        Route::get('/service/server/clear/price', [ServiceServerController::class, 'clearPrice'])->name('admin.server.clear.price');
    }
    Route::get('/service/server/delete', [ServiceServerController::class, 'serverDeleteAll'])->name('admin.server.delete.all');
    Route::get('ticket/ticket', [TicketsController::class, 'viewTicket'])->name('admin.ticket.ticket');
    Route::get('ticket/ticket/edit/{id}', [TicketsController::class, 'viewEditTicket'])->name('admin.ticket.ticket.edit');
    Route::post('ticket/ticket/update/{id}', [TicketsController::class, 'updateTicket'])->name('admin.ticket.ticket.update');
    Route::get('ticket/ticket/delete/{id}', [TicketsController::class, 'deleteTicket'])->name('admin.ticket.ticket.delete');

    Route::get('affiliates', [AffiliatesController::class, 'viewAffiliates'])->name('admin.affiliates');
    Route::post('affiliates/withdraw/{id}/', [AffiliatesController::class, 'withdrawRef'])->name('admin.affiliates.withdraw');


    Route::get('/service/server', [ServiceServerController::class, 'viewServer'])->name('admin.server');
    Route::get('/service/server/smm', [ServiceServerController::class, 'viewServerSmm'])->name('admin.server.smm');
    Route::get('/service/server/edit/{id}', [ServiceServerController::class, 'viewEditServer'])->name('admin.server.edit');
    Route::post('/service/server/update/{id}', [ServiceServerController::class, 'updateServer'])->name('admin.server.update');
    // update price
    Route::post('/service/server/update-price', [ServiceServerController::class, 'updatePrice'])->name('admin.server.update-price');

    //voucher
    Route::get('voucher', [VoucherController::class, 'viewVoucher'])->name('admin.voucher');
    Route::post('voucher/create', [VoucherController::class, 'createVoucher'])->name('admin.voucher.create');
    Route::post('/admin/voucher/{id}', [VoucherController::class, 'deleteVoucher'])->name('admin.voucher.delete');

    Route::get('users', [UserController::class, 'viewUser'])->name('admin.user');
    Route::get('user/{id}', [UserController::class, 'viewUserDetail'])->name('admin.user.detail');
    Route::post('user/update-lamtilo/{username}', [UserController::class, 'updateUser'])->name('admin.user.update');
    Route::post('user/update-password/{username}', [UserController::class, 'updatePassword'])->name('admin.user.update-password');
    Route::get('user/delete/{id}', [UserController::class, 'deleteUser'])->name('admin.user.delete');
    Route::get('user/edit/balance', [UserController::class, 'viewUserBalance'])->name('admin.user.balance');
    Route::post('user/update/balance', [UserController::class, 'updateUserBalance'])->name('admin.user.update-balance');
    Route::get('user/transactions/{username}', [UserController::class, 'viewUserTransactions'])->name('admin.user.transactions');

    Route::get('transactions', [HistoryController::class, 'viewUserHistory'])->name('admin.user.history');

    // Automation routes
    Route::get('automations', [AutomationController::class, 'index'])->name('admin.automation.index');
    Route::get('automations/create', [AutomationController::class, 'create'])->name('admin.automation.create');
    Route::post('automations', [AutomationController::class, 'store'])->name('admin.automation.store');
    Route::post('automation/{id}/status', [AutomationController::class, 'updateStatus'])->name('admin.automation.status');
    Route::post('automation/{id}/run', [AutomationController::class, 'runManual'])->name('admin.automation.run');
    Route::delete('automation/{id}', [AutomationController::class, 'destroy'])->name('admin.automation.destroy');

    Route::post('website/update', [DataAdminController::class, 'updateWebsiteConfig'])->name('admin.website.update');
    Route::post('website/setting', [DataAdminController::class, 'WebsiteSetting'])->name('admin.website.setting');
    Route::post('rules/update', [DataAdminController::class, 'updateRules'])->name('admin.rules.update');
    
    Route::get('history/products', [HistoryController::class, 'products'])->name('admin.history.products');
    Route::get('history/products/detail/{id}', [HistoryController::class, 'productDetail'])->name('admin.history.products.detail');
    Route::post('history/products/update/{id}', [HistoryController::class, 'updateOrder'])->name('admin.history.products.update');
    Route::post('history/products/delete/checked', [HistoryController::class, 'deleteProductChecked'])->name('admin.history.products.delete.checked');
    
    
    
    Route::get('history/orders', [HistoryController::class, 'viewHistoryOrders'])->name('admin.history.orders');
    Route::get('history/payment', [HistoryController::class, 'viewHistoryPayment'])->name('admin.history.payment');
    
    Route::post('action-order/{id}', [HistoryController::class, 'actionOrder'])->name('admin.order.action');
    Route::post('order-refund', [HistoryController::class, 'refundOrder'])->name('admin.refund.order');
    Route::get('order-delete/{id}', [HistoryController::class, 'deleteOrder'])->name('admin.order.delete');
    Route::get('order-send/{id}', [HistoryController::class, 'sendOrder'])->name('admin.order.send');
 
});

// Discord routes
Route::prefix('admin')->middleware(['auth', 'isAdmin'])->name('admin.')->group(function () {
    // Discord routes
    Route::get('discord/config', [DiscordController::class, 'viewDiscordConfig'])->name('discord.config');
    Route::get('discord/test-webhook', [DiscordController::class, 'testWebhook'])->name('discord.test-webhook');
    Route::get('discord/test-webhook-product', [DiscordController::class, 'testWebhookProduct'])->name('discord.test-webhook-product');

    // SMTP routes
    Route::get('smtp/config', [ViewAdminController::class, 'viewSmtpConfig'])->name('smtp.config');
    Route::post('smtp/update', [DataAdminController::class, 'updateSmtpConfig'])->name('smtp.update');
});
