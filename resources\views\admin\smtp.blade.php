@extends('admin.layouts.app')
@section('title', 'Notification | E-Mail | SMTP')
@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card custom-card">
            <div class="card-header">
                <h5 class="card-title">Notification | E-Mail | SMTP</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.smtp.update') }}" method="POST">
                    @csrf
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="smtp_host" name="smtp_host"
                                       placeholder="smtp.gmail.com" value="{{ siteValue('smtp_host') }}">
                                <label for="smtp_host">SMTP Host</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="number" class="form-control" id="smtp_port" name="smtp_port"
                                       placeholder="587" value="{{ siteValue('smtp_port') }}">
                                <label for="smtp_port">SMTP Port</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" id="smtp_user" name="smtp_user"
                                       placeholder="<EMAIL>" value="{{ siteValue('smtp_user') }}">
                                <label for="smtp_user">SMTP Username</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="password" class="form-control" id="smtp_pass" name="smtp_pass"
                                       placeholder="••••••••" value="{{ siteValue('smtp_pass') }}">
                                <label for="smtp_pass">SMTP Password</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="smtp_name" name="smtp_name"
                                       placeholder="Website Name" value="{{ siteValue('smtp_name') }}">
                                <label for="smtp_name">From Name</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Lưu cấu hình
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

</div>


@endsection
