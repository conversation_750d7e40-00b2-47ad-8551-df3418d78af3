<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductNotification extends Model
{
    use HasFactory;

    protected $fillable = [
        'email',
        'product_main_id',
        'domain',
        'status',
    ];

    /**
     * <PERSON>uan hệ với ProductMain
     */
    public function productMain()
    {
        return $this->belongsTo(ProductMain::class, 'product_main_id');
    }

    /**
     * Scope để lấy các đăng ký active
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope để lấy theo domain
     */
    public function scopeByDomain($query, $domain = null)
    {
        $domain = $domain ?: request()->getHost();
        return $query->where('domain', $domain);
    }
}
