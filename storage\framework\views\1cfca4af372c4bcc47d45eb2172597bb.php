 
<?php $__env->startSection('title', 'Sản phẩm - ' . $productMain->name); ?>
<?php $__env->startSection('style'); ?>
<style>
    .card-hover-top {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .card-hover-top:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
    }

    /* Phần mô tả */
    .description-section {
        background: #ffffff;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 25px;
        margin-right: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        position: relative;
    }

    .description-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #007bff, #0056b3);
        border-radius: 12px 12px 0 0;
    }

    .description-section h4 {
        color: #2c3e50;
        border-bottom: 2px solid #f8f9fa;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }

    .description-content {
        max-height: 500px;
        overflow-y: auto;
        padding-right: 15px;
        background: #fafbfc;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .description-content::-webkit-scrollbar {
        width: 8px;
    }

    .description-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .description-content::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
    }

    .description-content::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Phần gợi ý sản phẩm */
    .suggestions-section {
        background: #f8f9fa;
        border: 2px solid #dee2e6;
        border-radius: 12px;
        padding: 25px;
        margin-left: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        position: relative;
    }

    .suggestions-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #dc3545, #fd7e14);
        border-radius: 12px 12px 0 0;
    }

    .suggestions-section h4 {
        color: #2c3e50;
        border-bottom: 2px solid #ffffff;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }

    .suggestions-section h4 i {
        color: #dc3545;
    }

    /* Badge đã bán */
    .sold-badge .badge {
        background: linear-gradient(135deg, #dc3545, #c82333) !important;
        border: 2px solid #ffffff;
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        animation: pulse-glow 2s infinite;
        position: relative;
        overflow: hidden;
    }

    .sold-badge .badge::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
        transform: rotate(45deg);
        animation: shine 3s infinite;
    }

    .sold-badge .badge i {
        animation: fire-flicker 1.5s infinite alternate;
    }

    @keyframes pulse-glow {
        0%, 100% {
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
            transform: scale(1);
        }
        50% {
            box-shadow: 0 6px 20px rgba(220, 53, 69, 0.5);
            transform: scale(1.05);
        }
    }

    @keyframes shine {
        0% {
            left: -100%;
        }
        100% {
            left: 100%;
        }
    }

    @keyframes fire-flicker {
        0% {
            transform: scale(1) rotate(0deg);
        }
        100% {
            transform: scale(1.1) rotate(5deg);
        }
    }

    .suggested-products-sidebar .card {
        border: 2px solid #ffffff;
        border-radius: 8px;
        transition: all 0.3s ease;
        background: #ffffff;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }

    .suggested-products-sidebar .card:hover {
        border-color: #007bff;
        box-shadow: 0 4px 15px rgba(0,123,255,0.2);
        transform: translateY(-2px);
    }

    .suggested-products-sidebar .card-title {
        font-size: 0.75rem;
        font-weight: 600;
        color: #2c3e50;
    }

    /* Đường phân chia giữa 2 cột */
    .divider-line {
        position: relative;
    }

    .divider-line::after {
        content: '';
        position: absolute;
        top: 0;
        right: -15px;
        width: 2px;
        height: 100%;
        background: linear-gradient(180deg, #e9ecef, #dee2e6, #e9ecef);
        border-radius: 1px;
    }

    @media (max-width: 768px) {
        .description-section,
        .suggestions-section {
            margin: 0 0 20px 0;
            padding: 20px;
        }

        .divider-line::after {
            display: none;
        }

        .suggestions-section {
            border-top: 3px solid #dc3545;
        }

        .sold-badge {
            margin-top: 10px !important;
            margin-left: 0 !important;
        }

        .sold-badge .badge {
            font-size: 0.8rem !important;
            padding: 8px 16px !important;
        }

        .d-flex.justify-content-between {
            flex-direction: column !important;
            align-items: flex-start !important;
        }

        /* Fix ảnh gợi ý sản phẩm trên mobile */
        .suggested-products-sidebar .card {
            margin-bottom: 15px;
        }

        .suggested-products-sidebar .card .row {
            align-items: center;
        }

        .suggested-products-sidebar .card img {
            height: 60px !important;
            width: 100%;
            object-fit: cover;
            border-radius: 8px 0 0 8px;
        }

        .suggested-products-sidebar .card-body {
            padding: 10px !important;
        }

        .suggested-products-sidebar .card-title {
            font-size: 0.7rem !important;
            line-height: 1.1 !important;
            margin-bottom: 5px !important;
        }

        .suggested-products-sidebar .text-primary {
            font-size: 0.65rem !important;
        }

        .suggested-products-sidebar small {
            font-size: 0.6rem !important;
        }
    }
</style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?> 
    <div class="row">
         <div class="col-md-12">
            <div class="card mt-5"> 
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12 col-lg-4 col-xl-3 mb-1 mb-lg-0">
                            <div class="box-image-product bg-white rounded-2 py-2 px-0 px-md-2">
                                <img src="<?php echo e(asset($productMain->image)); ?>" alt="<?php echo e($productMain->name); ?>" class="img-fluid rounded w-100">
                            </div>
                        </div>
                        <div class="col-md-12 col-lg-8">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h4 class="fs-3 text-dark fw-bolder my-2 pb-2 border-bottom flex-grow-1"><?php echo e($productMain->name); ?></h4>
                                <div class="sold-badge ms-3 mt-2">
                                    <span class="badge bg-danger fs-6 px-3 py-2 rounded-pill shadow-sm">
                                        <i class="fas fa-fire me-1"></i>
                                        Đã bán: <?php echo e(number_format($productMain->sold >= 100 ? $productMain->sold : $productMain->sold + 100)); ?>

                                    </span>
                                </div>
                            </div>
                            <div class="my-2 fs-16">
                                <p class="mb-2">Có sẵn: <span class="text-success" id="inventory">
                                    <?php if($productMain->product->where('domain', request()->getHost())->where('status', 'active')->count() > 0): ?>
                                        <?php
                                            $firstProduct = $productMain->product->where('domain', request()->getHost())->where('status', 'active')->first();
                                            if (request()->getHost() === env('APP_MAIN_SITE')) {
                                                if($firstProduct->type == "taphoammo") {
                                                    $inventory = $firstProduct->stock;
                                                } else {
                                                    $inventory = \App\Models\ProductInventory::where('product_id', $firstProduct->id)
                                                        ->where('status', 'available')
                                                        ->where('domain', env('APP_MAIN_SITE'))
                                                        ->count();
                                                }
                                            } else {
                                                if($firstProduct->type == "taphoammo") {
                                                    $inventory = $firstProduct->stock;
                                                } else {
                                                    $inventory = \App\Models\ProductInventory::where('product_id', $firstProduct->id)
                                                        ->where('status', 'available')
                                                        ->where('domain', request()->getHost())
                                                        ->count();
                                                }
                                            }
                                        ?>
                                        <?php echo e(number_format($inventory)); ?>

                                    <?php else: ?>
                                        0
                                    <?php endif; ?>
                                </span></p>
                                <p>Giá: <span class="text-danger" id="price">
                                    <?php if($productMain->product->where('domain', request()->getHost())->where('status', 'active')->count() > 0): ?>
                                        <?php echo e(number_format($productMain->product->where('domain', request()->getHost())->where('status', 'active')->first()->price)); ?> ₫
                                    <?php else: ?>
                                        0 ₫
                                    <?php endif; ?>
                                </span></p>
                            </div>
                            <div class="mb-3">
                                <p class="text-sm text-muted mb-1">Loại:</p>
                                <div class="list-input-radio">
                                 <?php $__currentLoopData = $productMain->product->where('domain', request()->getHost())->where('status', 'active'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $prod): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            if (request()->getHost() === env('APP_MAIN_SITE')) {
                                                if($prod->type == "taphoammo") {
                                                    $inventory = $prod->stock;
                                                } else {
                                                    $inventory = \App\Models\ProductInventory::where(
                                                        'product_id',
                                                        $prod->id,
                                                    )
                                                        ->where('status', 'available')
                                                        ->where('domain', env('APP_MAIN_SITE'))
                                                        ->count();
                                                }
                                            } else {
                                                if($prod->type == "taphoammo") {
                                                    $inventory = $prod->stock;
                                                } else {
                                                    $inventory = \App\Models\ProductInventory::where(
                                                        'product_id',
                                                        $prod->id,
                                                    )
                                                        ->where('status', 'available')
                                                        ->where('domain', request()->getHost())
                                                        ->count();
                                                }
                                            }
                                        ?>

                                        
                                                                                                                        <div class="">
                                                <input type="radio" name="product" id="product-<?php echo e($prod->id); ?>"
                                                value="<?php echo e($prod->id); ?>"   onchange="changeProduct()" data-price="<?php echo e($prod->price); ?>"
                                                <?php echo e($loop->first ? 'checked' : ''); ?> data-inventory="<?php echo e($inventory); ?>"
                                               >
                                                <label for="product-<?php echo e($prod->id); ?>">   
                                                                                            <?php echo e($prod->name); ?>

                                                                                                </label>
                                            </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                                                            </div>
                            </div>
                            <div class="mb-3 row">
                                <div class="col-md-3">
                                    <label for="quantity" class="form-label">Số lượng: </label>
                                    <input type="number" class="form-control" name="quantity" placeholder="Nhập số lượng"
                                        onkeyup="caculatePrice()">
                                </div>
                                <?php if($productMain->is_email): ?>
                                    <div class="col-md-4">
                                        <label for="email" class="form-label">Email: </label>
                                        <input type="email" class="form-control" name="email" placeholder="Nhập email">
                                    </div>
                                <?php endif; ?>
                                <?php if($productMain->is_phone): ?>
                                    <div class="col-md-4">
                                        <label for="phone" class="form-label">Số điện thoại: </label>
                                        <input type="text" class="form-control" name="phone"
                                            placeholder="Nhập số điện thoại">
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="">
                                <button type="button" id="btn-info-product" onclick="showInfoProduct()"
                                    class="btn btn-primary btn-sm">Mua hàng</button>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8 divider-line">
                            <div class="description-section">
                                <h4 class="fs-3 text-start mb-3">
                                    <i class="fas fa-file-alt me-2"></i>Mô Tả Sản Phẩm
                                </h4>
                                <div class="description-content">
                                    <?php echo $productMain->description; ?>

                                </div>
                            </div>
                        </div>

                        <?php if($suggestedProducts && $suggestedProducts->count() > 0): ?>
                        <div class="col-md-4">
                            <div class="suggestions-section">
                                <h4 class="fs-3 text-start mb-3">
                                    <i class="fas fa-fire me-2"></i>Sản Phẩm Bán Chạy
                                </h4>
                                <div class="suggested-products-sidebar">
                                <?php $__currentLoopData = $suggestedProducts->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="mb-3">
                                    <a href="<?php echo e(route('client.product', ['slug' => $product->slug])); ?>" class="text-decoration-none">
                                        <div class="card shadow-sm card-hover-top overflow-hidden position-relative">
                                            <div class="row g-0">
                                                <div class="col-4">
                                                    <div class="position-relative">
                                                        <img src="<?php echo e(asset($product->image)); ?>" class="img-fluid rounded-start h-100" alt="<?php echo e($product->name); ?>" style="object-fit: cover; height: 80px; min-height: 60px;">
                                                        <div class="position-absolute top-0 start-0 bg-danger text-white px-2 py-1" style="font-size: 0.6rem; border-radius: 0 0 8px 0;">
                                                            <i class="fas fa-fire"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-8">
                                                    <div class="card-body p-2">
                                                        <h6 class="card-title fs-12 mb-1 text-dark" style="line-height: 1.2; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">
                                                            <?php echo e($product->name); ?>

                                                        </h6>
                                                        <div class="text-primary fs-11">
                                                            <?php
                                                                $activeProducts = $product->product->where('domain', request()->getHost())->where('status', 'active');
                                                            ?>
                                                            <?php if($activeProducts->count() > 1): ?>
                                                                <span class="text-danger fw-bold">
                                                                    <i class="fas fa-tag me-1"></i><?php echo e(number_format($product->getPriceStart())); ?>đ - <?php echo e(number_format($product->getPriceEnd())); ?>đ
                                                                </span>
                                                            <?php elseif($activeProducts->count() == 1): ?>
                                                                <span class="text-danger fw-bold">
                                                                    <i class="fas fa-tag me-1"></i><?php echo e(number_format($activeProducts->first()->price)); ?>đ
                                                                </span>
                                                            <?php else: ?>
                                                                <span class="text-muted">
                                                                    <i class="fas fa-phone me-1"></i>Liên hệ
                                                                </span>
                                                            <?php endif; ?>
                                                        </div>
                                                        <small class="text-muted d-block mt-1">
                                                            <i class="fas fa-shopping-cart me-1"></i><?php echo e(number_format($product->sold)); ?> lượt mua
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                <?php if($suggestedProducts->count() >= 5): ?>
                                <div class="text-center">
                                    <a href="<?php echo e(route('client.product.categories')); ?>" class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-plus me-1"></i>Xem thêm sản phẩm
                                    </a>
                                </div>
                                <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    
    <div class="modal fade" id="modal-info" tabindex="-1" aria-labelledby="modal-infoLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header py-3">
                    <h4 class="fs-4 text-primary card-title">Xác nhận đơn hàng</h4>
                </div>
                <div class="modal-body">
                    <div class="text-start">
                        <small class="text-danger mb-3">Vui lòng xác nhận đơn hàng:</small>
                        <div class="mb-0">
                            <p class="mb-2">Sản phẩm: <span class="fw-bold"  id="name_product"><?php echo e($productMain->name); ?></span></p>
                            <p class="mb-2">Số lượng: <span class="fw-bold" id="info-quantity">0</span></p>
                            <p class="mb-2 text-success">Tổng tiền: <span class="fw-bold" id="total-price">0</span></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <input type="hidden" name="_token" value="<?php echo e(csrf_token()); ?>">
                    <button type="button" class="btn btn-primary btn-sm" id="btn-buy-product" onclick="buyProduct()">Xác
                        nhận</button>
                    <button type="button" class="btn btn-danger btn-sm" data-bs-dismiss="modal">Đóng</button>
                </div>
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>
 
    <script>
        $(document).ready(function() {

            $('input[name="quantity"]').val(1);
            changeProduct();
        });

        function changeProduct() {
            let price = $('input[name="product"]:checked').data('price');
            let inventory = $('input[name="product"]:checked').data('inventory');

            // Cập nhật hiển thị số lượng
            if (inventory == 0) {
                $('#inventory').text('Hết hàng');
                $('#btn-info-product').attr('disabled', true);
            } else {
                $('#inventory').text(formatNumber(inventory));
                $('#btn-info-product').attr('disabled', false);
            }

            // Cập nhật hiển thị giá
            if (typeof formatCurrency === 'function') {
                $('#price').text(formatCurrency(price));
            } else {
                $('#price').text(new Intl.NumberFormat('vi-VN').format(price) + ' ₫');
            }

            // Cập nhật tên sản phẩm nếu có element
            if ($('#name_product').length) {
                $('#name_product').text($('input[name="product"]:checked').data('name'));
            }

            caculatePrice();
        }

        function showInfoProduct() {
            let product = $('input[name="product"]:checked').val();
            let quantity = $('input[name="quantity"]').val();
            let email = $('input[name="email"]').val();
            let phone = $('input[name="phone"]').val();

            // show modal
            $('#modal-info').modal('show');
        }

        function caculatePrice() {
            let price = $('input[name="product"]:checked').data('price');
            let quantity = $('input[name="quantity"]').val() || 1;
            let total = price * quantity;

            // Cập nhật tổng giá nếu có element total-price
            if ($('#total-price').length) {
                if (typeof formatCurrency === 'function') {
                    $('#total-price').text(formatCurrency(total));
                } else {
                    $('#total-price').text(new Intl.NumberFormat('vi-VN').format(total) + ' ₫');
                }
            }

            // Cập nhật số lượng nếu có element info-quantity
            if ($('#info-quantity').length) {
                $('#info-quantity').text(quantity);
            }
        }

        function buyProduct() {
            let product = $('input[name="product"]:checked').val();
            let quantity = $('input[name="quantity"]').val();
            let email = $('input[name="email"]').val();
            let phone = $('input[name="phone"]').val();

            if (quantity == 0) {
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi',
                    text: 'Vui lòng nhập số lượng',
                });
                return;
            }
            

            $.ajax({
                url: '/api/product/buy',
                type: 'POST',
                headers: {
                    'X-ACCESS-TOKEN': $('meta[name="access-token"]').attr('content')
                },

                data: {
                    product_id: product,
                    quantity: quantity,
                    email: email,
                    phone: phone,
                    _token: $('input[name="_token"]').val()
                },
                beforeSend: function() {
                    $('#btn-buy-product').attr('disabled', true).html(
                        `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Đang xử lý...`
                    );
                },
                complete: function() {
                    $('#btn-buy-product').attr('disabled', false).html('Xác nhận');
                },
                success: function(res) {
                    if (res.status == 'success') {
                        // Cập nhật số lượng kho sau khi mua thành công
                        updateInventoryAfterPurchase();

                        Swal.fire({
                            icon: 'success',
                            title: 'Thành công',
                            text: 'Đặt hàng thành công',
                        }).then((result) => {
                            window.location.reload();
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi',
                            text: res.message,
                        });
                    }
                },
                error: function(err) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Lỗi',
                        text: err?.responseJSON?.message || 'Có lỗi xảy ra, vui lòng thử lại sau',
                    });
                },
            });
        }

        function updateInventoryAfterPurchase() {
            let currentInventory = parseInt($('input[name="product"]:checked').data('inventory'));
            let purchasedQuantity = parseInt($('input[name="quantity"]').val());
            let newInventory = currentInventory - purchasedQuantity;

            // Cập nhật data attribute
            $('input[name="product"]:checked').data('inventory', newInventory);

            // Cập nhật hiển thị
            if (newInventory <= 0) {
                $('#inventory').text('Hết hàng');
                $('#btn-info-product').attr('disabled', true);
            } else {
                $('#inventory').text(formatNumber(newInventory));
                $('#btn-info-product').attr('disabled', false);
            }
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('guard.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Src Khách\maxsocial\resources\views/guard/product/index.blade.php ENDPATH**/ ?>