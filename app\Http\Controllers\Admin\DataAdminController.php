<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Library\TelegramSdk;
use App\Models\Config;
use App\Models\ConfigSite;
use Illuminate\Http\Request;
use App\Models\Service;
use App\Models\ServicePlatform;
use App\Models\ServerAction;

use App\Models\ProductCategory;
use App\Models\Product;
use App\Models\ProductMain;
use App\Models\OrderProduct;
use App\Models\ProductInventory;
use App\Models\ServiceServer;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class DataAdminController extends Controller
{
    public function getData(Request $request)
    {
        $model = $request->model;

        // datatable
        $start = $request->start ? $request->start : 0;
        $length = $request->length ? $request->length : 10;

        if ($request->length == -1) {
            $length = 100000;
        }

        $search = $request->search['value'] ? $request->search['value'] : '';
        if (isset($request->order)) {
            $order = $request->columns[$request->order[0]['column']]['data'];
        } else {
            $order = 'id';
        }
        if (isset($request->order)) {
            $dir = $request->order[0]['dir'];
        } else {
            $dir = 'asc';
        }
        $data = [];
        $total = 0;
        $filtered = 0;

        switch ($model) {
             
            case 'categories':
                $data = ProductCategory::search($search)
                    ->where('domain', request()->getHost())
                    ->orderBy($order, $dir)
                    ->offset($start)
                    ->limit($length)
                    ->get();

                $total = ProductCategory::where('domain', request()->getHost())->count();
                $filtered = ProductCategory::search($search)->count();
                break;
            case 'products-main':
                $data = ProductMain::search($search)
                    ->where('domain', env("APP_MAIN_SITE"))
                    ->orderBy($order, $dir)
                    ->offset($start)
                    ->limit($length)
                    ->get();

                $total = ProductMain::where('domain', request()->getHost())->count();
                $filtered = ProductMain::search($search)->count();
                break;
            case 'products':
                $data = Product::search($search)
                    ->where('domain', request()->getHost())
                    ->orderBy($order, $dir)
                    ->offset($start)
                    ->limit($length)
                    ->get();

                $data->load('productMain');
                
                $data->map(function ($item) {
                    if ($item->type === 'taphoammo') {
                        $item->inventory = $item->stock;
                    } else {
                        $item->inventory = $item->inventory()
                            ->where('domain', request()->getHost())
                            ->where('status', 'available')
                            ->count();
                    }
                    return $item;
                });


                $total = Product::where('domain', request()->getHost())->count();
                $filtered = Product::search($search)->count();
                break;
            case 'products.child.inventory':
                $product = Product::where('id', $request->product_id)->where('domain', request()->getHost())->first();

                if (!$product) {
                    return response()->json([
                        'draw' => $request->draw,
                        'recordsTotal' => 0,
                        'recordsFiltered' => 0,
                        'data' => []
                    ]);
                }

                $data = $product->inventory()->where('domain', request()->getHost())
                    ->search($search)
                    ->orderBy($order, $dir)
                    ->offset($start)
                    ->limit($length)
                    ->get();

                $total = $product->inventory()->where('domain', request()->getHost())->count();
                $filtered = $product->inventory()->where('domain', request()->getHost())->search($search)->count();
                break;
            case 'orders-product':

                if($search !== null && $search !== '')
                {
                    $data = OrderProduct::where('domain', request()->getHost())
                    ->where(function($query) use ($search) {
                        $query->where("order_id", "like", "%" . $search . "%")
                              ->orWhereHas('user', function($q) use ($search) {
                                  $q->where('username', 'like', '%' . $search . '%');
                              })
                              ->orWhereHas('product', function($q) use ($search) {
                                  $q->where('name', 'like', '%' . $search . '%');
                              })
                              ->orWhere('email', 'like', '%' . $search . '%')
                              ->orWhere('phone', 'like', '%' . $search . '%');
                    })
                    ->orderBy($order, $dir)
                    ->offset($start)
                    ->limit($length)
                    ->get();

                    $filtered = OrderProduct::where('domain', request()->getHost())
                    ->where(function($query) use ($search) {
                        $query->where("order_id", "like", "%" . $search . "%")
                              ->orWhereHas('user', function($q) use ($search) {
                                  $q->where('username', 'like', '%' . $search . '%');
                              })
                              ->orWhereHas('product', function($q) use ($search) {
                                  $q->where('name', 'like', '%' . $search . '%');
                              })
                              ->orWhere('email', 'like', '%' . $search . '%')
                              ->orWhere('phone', 'like', '%' . $search . '%');
                    })
                    ->count();
                }
                else{
                    $data = OrderProduct::where('domain', request()->getHost())
                    ->orderBy($order, $dir)
                    ->offset($start)
                    ->limit($length)
                    ->get();

                    $filtered = OrderProduct::where('domain', request()->getHost())->count();
                }

                $data->load(['user', 'product']);

                // Thêm order_id nếu chưa có và xử lý dữ liệu
                $data->each(function ($item) {
                    if (empty($item->order_id)) {
                        $item->order_id = 'ORD-' . str_pad($item->id, 6, '0', STR_PAD_LEFT);
                    }

                    // Đảm bảo product tồn tại - sử dụng accessor
                    if (!$item->product) {
                        $item->product = (object)[
                            'id' => null,
                            'name' => $item->product_name // Sử dụng accessor
                        ];
                    }

                    // Đảm bảo user tồn tại - sử dụng accessor
                    if (!$item->user) {
                        $item->user = (object)[
                            'id' => null,
                            'username' => $item->username // Sử dụng accessor
                        ];
                    }

                    // Thêm thông tin trạng thái dữ liệu
                    $item->data_status = $item->data_status;
                });

                $total = OrderProduct::where('domain', request()->getHost())->count();

                // Debug info
                \Log::info('Admin OrderProduct query result', [
                    'total_records' => $total,
                    'filtered_records' => $filtered,
                    'data_count' => $data->count(),
                    'domain' => request()->getHost(),
                    'search' => $search
                ]);

                break;
             
        }

        return response()->json([
            'draw' => $request->draw,
            'recordsTotal' => $total,
            'recordsFiltered' => $filtered,
            'data' => $data
        ]);
    }

    public function deleteData(Request $request)
    {
        $key = $request->key;
        $id = $request->id;

        switch ($key) {
             
            case 'categories':
                ProductCategory::where('id', $id)->where('domain', request()->getHost())->delete();
                break;
            case 'products-main':
                ProductMain::where('id', $id)->where('domain', request()->getHost())->delete();
                break;
            case 'products':
                $product = Product::where('id', $id)->where('domain', request()->getHost())->first();

                if ($product) {
                    $product->inventory()->delete();
                    $product->delete();
                }
                break;
            case 'products.child.inventory':
                $product = ProductInventory::where('id', $id)->where('domain', request()->getHost())->first();
                if ($product) {
                    $product->delete();
                }
                break;
            case 'orders-product':
                OrderProduct::where('id', $id)->where('domain', request()->getHost())->delete();
                break;
             
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Xóa thành công dư liệu'
        ]);
    }
    public function serviceChecking(Request $request)
    {
        $valid = Validator::make($request->all(), [
            "id" => "required",
        ]);

        if ($valid->fails()) {
            return resApi("error", $valid->errors()->first());
        } else {
            $lam = explode("_", $request->id);
            $social = ServicePlatform::where("domain", env("APP_MAIN_SITE"))
                ->where("id", $lam[0])
                ->first();
            if ($social) {
                $service_list = Service::where("domain", env("APP_MAIN_SITE"))
                    ->where("platform_id", $social->id)
                    ->get();

                $service_list = $service_list->map(function ($service) use (
                    $social
                ) {
                    $service->social_image = $social->image;
                    return $service;
                });
                if ($service_list->isNotEmpty()) {
                    return resApi("success", "Thành công", $service_list);
                } else {
                    return resApi("error", "Không tìm thấy dịch vụ");
                }
            } else {
                return resApi("error", "Không tìm thấy dịch vụ");
            }
        }
    }

    public function serverChecking(Request $request)
    {
        // Xác thực dữ liệu đầu vào
        $valid = Validator::make($request->all(), [
            "id" => "required|string",
        ]);

        if ($valid->fails()) {
            return resApi("error", $valid->errors()->first());
        }

        // Tách id từ request
        $lam = explode("-", $request->id);
        $serviceId = $lam[0];

        // Tìm dịch vụ
        $service = Service::where("domain", env("APP_MAIN_SITE"))
            ->where("id", $serviceId)
            ->first();

        if ($service) {
            // Lấy thông tin nền tảng xã hội
            $social = ServicePlatform::where("domain", env("APP_MAIN_SITE"))
                ->where("id", $service->platform_id)
                ->first();

            // Lấy danh sách dịch vụ
            $service_list = ServiceServer::where("domain", getDomain())
                ->where("service_id", $service->id)
                ->get();

            // Xử lý danh sách dịch vụ
            $service_list = $service_list->map(function ($service) use (
                $social
            ) {
                // Lấy thông tin từ ServerAction cho từng service
                $serviceAction = ServerAction::where("domain", getDomain())
                    ->where("server_id", $service->id)
                    ->first();

                // Gán thông tin nền tảng xã hội và xóa các thuộc tính không cần thiết
                $service->social_image = $social->image;
                unset($service->providerName);
                unset($service->providerLink);
                unset($service->providerServer);
                unset($service->providerKey);

                // Gán thông tin từ ServerAction nếu có
                if ($serviceAction) {
                    $service->quantity_status = $serviceAction->quantity_status;
                    $service->comments_status = $serviceAction->comments_status;
                    $service->reaction_status = $serviceAction->reaction_status;
                    $service->get_uid = $serviceAction->get_uid;
                    $service->minutes_status = $serviceAction->minutes_status;
                    $service->reaction_data = $serviceAction->reaction_data;
                    $service->comments_data = $serviceAction->comments_data;
                    $service->minutes_data = $serviceAction->minutes_data;
                    $service->posts_status = $serviceAction->posts_status;
                    $service->posts_data = $serviceAction->posts_data;
                    $service->time_status = $serviceAction->time_status;
                    $service->time_data = $serviceAction->time_data;
                    $service->price_user = $service->levelPrice(
                        Auth::user()->level
                    );
                }

                return $service;
            });

            // Kiểm tra và trả về dữ liệu
            if ($service_list->isNotEmpty()) {
                return resApi("success", "Thành công", $service_list);
            } else {
                return resApi("error", "Không tìm thấy dịch vụ", "false");
            }
        } else {
            return resApi("error", "Không tìm thấy dịch vụ");
        }
    }

    public function serverserviceChecking(Request $request)
    {
        // Validate the request
        $valid = Validator::make($request->all(), [
            "id" => "required",
        ]);

        if ($valid->fails()) {
            return resApi("error", $valid->errors()->first());
        }
        $lam = explode("-", $request->id);
        $lam1 = $lam[1];
        $lam12 = explode("_", $lam1);
        // Retrieve the service based on domain and id
        $services = ServiceServer::where("domain", getDomain())
            ->where("id", $lam12[1])
            ->first();

        if ($services) {
            // Retrieve the server action based on domain and server_id
            $service = ServerAction::where("domain", getDomain())
                ->where("server_id", $services->id)
                ->first();

            if ($service) {
                // Update the services status from the server action
                $services->minutes_status = $service->minutes_status;
                $services->quantity_status = $service->quantity_status;
                $services->reaction_status = $service->reaction_status;
                $services->posts_status = $service->posts_status;
                $services->time_status = $service->time_status;
                $services->duration_status = $service->duration_status;
                $services->comments_status = $service->comments_status;
                $services->price_user = $services->levelPrice(
                    Auth::user()->level
                );
            }

            unset($services->providerName);
            unset($services->price_distributor);
            unset($services->price_agency);
            unset($services->price_collaborator);
            unset($services->price);

            unset($services->price_update);
            unset($services->providerLink);
            unset($services->providerServer);
            unset($services->providerKey); // Xóa thuộc tính providerName

            // Manually set quantity_status to 'on'

            return resApi("success", "Thành công", $services);
        } else {
            return resApi("error", "Không tìm thấy dịch vụ");
        }
    }

    public function updateWebsiteConfig(Request $request)
    {
        $site_config = ConfigSite::where("domain", request()->getHost())
            ->where("status", "active")
            ->first();

        foreach ($request->all() as $key => $value) {
            if ($key != "_token") {
                $site_config->$key = $value;
            }
        }

        $site_config->save();

        return redirect()
            ->back()
            ->with("success", __("Website config updated"));
    }

    public function WebsiteSetting(Request $request)
    {
        $config = Config::first();

        foreach ($request->all() as $key => $value) {
            if ($key != "_token") {
                $config->$key = $value;
            }
        }

        $config->save();

        return redirect()
            ->back()
            ->with("success", __("Cập nhật thành công"));
    }

    public function updateRules(Request $request)
    {
        $site_config = ConfigSite::where("domain", request()->getHost())
            ->where("status", "active")
            ->first();

        if (!$site_config) {
            return redirect()
                ->back()
                ->with("error", "Không tìm thấy cấu hình website");
        }

        $site_config->rule = $request->rule;
        $site_config->save();

        return redirect()
            ->back()
            ->with("success", "Cập nhật quy tắc thành công");
    }

    public function updateSmtpConfig(Request $request)
    {
        $request->validate([
            'smtp_host' => 'required|string',
            'smtp_port' => 'required|numeric|min:1|max:65535',
            'smtp_user' => 'required|email',
            'smtp_pass' => 'required|string',
            'smtp_name' => 'required|string',
        ]);

        // Kiểm tra SMTP host không được là email address
        if (filter_var($request->smtp_host, FILTER_VALIDATE_EMAIL)) {
            return redirect()
                ->back()
                ->with("error", "SMTP Host không được là địa chỉ email. Ví dụ đúng: smtp.gmail.com")
                ->withInput();
        }

        $site_config = ConfigSite::where("domain", request()->getHost())
            ->where("status", "active")
            ->first();

        if (!$site_config) {
            return redirect()
                ->back()
                ->with("error", "Không tìm thấy cấu hình website");
        }

        $site_config->smtp_host = $request->smtp_host;
        $site_config->smtp_port = $request->smtp_port;
        $site_config->smtp_user = $request->smtp_user;
        $site_config->smtp_pass = $request->smtp_pass;
        $site_config->smtp_name = $request->smtp_name;
        $site_config->save();

        return redirect()
            ->back()
            ->with("success", "Cấu hình SMTP đã được cập nhật thành công");
    }


}
